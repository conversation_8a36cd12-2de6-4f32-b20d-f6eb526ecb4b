package com.anytech.anytxn.parameter.common.service.card;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.card.service.CardFaceServiceImpl;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFaceMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFaceSelfMapper;
import com.anytech.anytxn.parameter.base.card.service.ICardFaceLayoutInfoService;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceResDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceLayoutInfoDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardFace;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CardFaceServiceTest test class
 * 
 * <AUTHOR> Engineer
 * @date 2025-01-23
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CardFaceServiceTest {

    @Mock
    private ParmCardFaceSelfMapper parmCardFaceSelfMapper;
    
    @Mock
    private ParmCardFaceMapper parmCardFaceMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;
    
    @Mock
    private ICardFaceLayoutInfoService cardFaceLayoutInfoService;

    @InjectMocks
    private CardFaceServiceImpl cardFaceService;

    private ParmCardFace parmCardFace;
    private CardFaceReqDTO cardFaceReqDTO;
    private CardFaceResDTO cardFaceResDTO;
    private CardFaceLayoutInfoDTO cardFaceLayoutInfoDTO;

    @BeforeEach
    void setUp() {
        // Mock OrgNumberUtils static method to avoid NPE in BaseParam constructor
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            
            // Create test entity object
            parmCardFace = new ParmCardFace();
            parmCardFace.setId("1");
            parmCardFace.setOrganizationNumber("1001");
            parmCardFace.setTableId("FACE001");
            parmCardFace.setDescription("Test Card Face");
            parmCardFace.setTrackType("H");
            parmCardFace.setServiceCode("101");
            parmCardFace.setVirtualCardFlag("1");
            parmCardFace.setStatus("1");
            parmCardFace.setEmbossFlag("1");
            parmCardFace.setExpireRenewCardFlag("1");
            parmCardFace.setPasswordLetterFlag("1");
            parmCardFace.setPasswordEmbossDelayDays(new BigDecimal("7"));
            parmCardFace.setDomesticMailingFlag("1");
            parmCardFace.setCreateTime(LocalDateTime.now());
            parmCardFace.setUpdateTime(LocalDateTime.now());
            parmCardFace.setVersionNumber(1L);

            // Create test request DTO object
            cardFaceReqDTO = new CardFaceReqDTO();
            cardFaceReqDTO.setId("1");
            cardFaceReqDTO.setOrganizationNumber("1001");
            cardFaceReqDTO.setTableId("FACE001");
            cardFaceReqDTO.setDescription("Test Card Face");
            cardFaceReqDTO.setTrackType("H");
            cardFaceReqDTO.setServiceCode("101");
            cardFaceReqDTO.setVirtualCardFlag("1");
            cardFaceReqDTO.setStatus("1");
            cardFaceReqDTO.setEmbossFlag("1");
            cardFaceReqDTO.setExpireRenewCardFlag("1");
            cardFaceReqDTO.setPasswordLetterFlag("1");
            cardFaceReqDTO.setPasswordEmbossDelayDays(new BigDecimal("7"));
            cardFaceReqDTO.setDomesticMailingFlag("1");
            cardFaceReqDTO.setUpdateBy("testUser");
            cardFaceReqDTO.setVersionNumber(1L);

            // Create test response DTO object
            cardFaceResDTO = new CardFaceResDTO();
            cardFaceResDTO.setId("1");
            cardFaceResDTO.setOrganizationNumber("1001");
            cardFaceResDTO.setTableId("FACE001");
            cardFaceResDTO.setDescription("Test Card Face");
            cardFaceResDTO.setTrackType("H");
            cardFaceResDTO.setServiceCode("101");
            cardFaceResDTO.setVirtualCardFlag("1");
            cardFaceResDTO.setStatus("1");
            cardFaceResDTO.setEmbossFlag("1");
            cardFaceResDTO.setExpireRenewCardFlag("1");
            cardFaceResDTO.setPasswordLetterFlag("1");
            cardFaceResDTO.setPasswordEmbossDelayDays(new BigDecimal("7"));
            cardFaceResDTO.setDomesticMailingFlag("1");
            cardFaceResDTO.setCreateTime(LocalDateTime.now());
            cardFaceResDTO.setUpdateTime(LocalDateTime.now());
            cardFaceResDTO.setUpdateBy("testUser");
            cardFaceResDTO.setVersionNumber(1L);

            // Create CardFaceLayoutInfoDTO test object
            cardFaceLayoutInfoDTO = new CardFaceLayoutInfoDTO();
            cardFaceLayoutInfoDTO.setOrganizationNumber("1001");
            cardFaceLayoutInfoDTO.setTableId("FACE001");
            cardFaceLayoutInfoDTO.setCardLayoutCode("LAYOUT001");
            cardFaceLayoutInfoDTO.setDescription("Test Layout");

            cardFaceReqDTO.setCardFaceLayoutInfoDTOList(Arrays.asList(cardFaceLayoutInfoDTO));
            cardFaceResDTO.setCardFaceLayoutInfoDTOList(Arrays.asList(cardFaceLayoutInfoDTO));
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        Mockito.lenient().when(parmCardFaceSelfMapper.selectByOrgAndTableId("1001", "FACE001")).thenReturn(parmCardFace);
        Mockito.lenient().when(cardFaceLayoutInfoService.queryByOrgAndFaceId("1001", "FACE001")).thenReturn(Arrays.asList(cardFaceLayoutInfoDTO));
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardFace, CardFaceResDTO.class))
                          .thenReturn(cardFaceResDTO);

            // Act
            CardFaceResDTO result = cardFaceService.findByOrgAndTableId("1001", "FACE001");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("FACE001", result.getTableId());
            assertEquals(1, result.getCardFaceLayoutInfoDTOList().size());
            verify(parmCardFaceSelfMapper).selectByOrgAndTableId("1001", "FACE001");
            verify(cardFaceLayoutInfoService).queryByOrgAndFaceId("1001", "FACE001");
        }
    }

    @Test
    void testFindByOrgAndTableId_NotFound() {
        // Arrange
        Mockito.lenient().when(parmCardFaceSelfMapper.selectByOrgAndTableId("1001", "FACE999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardFaceService.findByOrgAndTableId("1001", "FACE999"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_FACE_FAULT.getCode(), exception.getErrCode());
        verify(parmCardFaceSelfMapper).selectByOrgAndTableId("1001", "FACE999");
    }

    @Test
    void testAdd_Success() {
        // Arrange
        Mockito.lenient().when(parmCardFaceSelfMapper.isExists("1001", "FACE001")).thenReturn(0);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);
        Mockito.lenient().doNothing().when(cardFaceLayoutInfoService).addCardFaceLayoutCodes(any(CardFaceReqDTO.class));
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class)) {
            
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockTenantUtils.when(() -> TenantUtils.getTenantId()).thenReturn("tenant1");

            // Act
            ParameterCompare result = cardFaceService.add(cardFaceReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("FACE001", result.getMainParmId());
            verify(parmCardFaceSelfMapper).isExists("1001", "FACE001");
            verify(numberIdGenerator).generateId("tenant1");
            verify(cardFaceLayoutInfoService).addCardFaceLayoutCodes(cardFaceReqDTO);
        }
    }

    @Test
    void testAdd_NullRequest() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardFaceService.add(null));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testAdd_AlreadyExists() {
        // Arrange
        Mockito.lenient().when(parmCardFaceSelfMapper.isExists("1001", "FACE001")).thenReturn(1);
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> cardFaceService.add(cardFaceReqDTO));
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_QUERY_PARM_CARD_FACE_FAULT.getCode(), exception.getErrCode());
            verify(parmCardFaceSelfMapper).isExists("1001", "FACE001");
        }
    }

    @Test
    void testRemoveById_Success() {
        // Arrange
        Mockito.lenient().when(parmCardFaceMapper.selectByPrimaryKey("1")).thenReturn(parmCardFace);
        Mockito.lenient().doNothing().when(cardFaceLayoutInfoService).deleteByOrgAndFaceId("1001", "FACE001");

        // Act
        ParameterCompare result = cardFaceService.removeById("1");

        // Assert
        assertNotNull(result);
        assertEquals("FACE001", result.getMainParmId());
        verify(parmCardFaceMapper).selectByPrimaryKey("1");
        verify(cardFaceLayoutInfoService).deleteByOrgAndFaceId("1001", "FACE001");
    }

    @Test
    void testRemoveById_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardFaceService.removeById(null));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testRemoveById_NotFound() {
        // Arrange
        Mockito.lenient().when(parmCardFaceMapper.selectByPrimaryKey("999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardFaceService.removeById("999"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_FACE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmCardFaceMapper).selectByPrimaryKey("999");
    }