package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationCycleDay;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationCycleDaySelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 组织机构参数服务测试类
 * 测试 OrganizationInfoServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class OrganizationInfoServiceTest {

    @Mock
    private ParmOrganizationInfoMapper parmOrganizationInfoMapper;

    @Mock
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Mock
    private ParmOrganizationCycleDaySelfMapper cycleDaySelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private OrganizationInfoServiceImpl organizationInfoService;

    private OrganizationInfoReqDTO organizationInfoReqDTO;
    private ParmOrganizationInfo parmOrganizationInfo;
    private OrganizationInfoResDTO organizationInfoResDTO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        organizationInfoReqDTO = new OrganizationInfoReqDTO();
        organizationInfoReqDTO.setId("1234567890123456");
        organizationInfoReqDTO.setOrganizationNumber("001");
        organizationInfoReqDTO.setDescription("测试机构");
        organizationInfoReqDTO.setAddress("测试地址");
        organizationInfoReqDTO.setCity("北京");
        organizationInfoReqDTO.setCountry("中国");
        organizationInfoReqDTO.setStatus("0");
        organizationInfoReqDTO.setProcessTodayFlag("1");
        organizationInfoReqDTO.setAutoGenPymtDays(5L);
        organizationInfoReqDTO.setMarkupFeeRate(new BigDecimal("0.05"));
        organizationInfoReqDTO.setVersionNumber(1L);
        organizationInfoReqDTO.setToday(LocalDate.now());
        organizationInfoReqDTO.setNextProcessingDay(LocalDate.now().plusDays(1));
        organizationInfoReqDTO.setLastProcessingDay(LocalDate.now().minusDays(1));
        organizationInfoReqDTO.setCreateTime(LocalDateTime.now());
        organizationInfoReqDTO.setUpdateTime(LocalDateTime.now());

        parmOrganizationInfo = new ParmOrganizationInfo();
        parmOrganizationInfo.setId("1234567890123456");
        parmOrganizationInfo.setOrganizationNumber("001");
        parmOrganizationInfo.setDescription("测试机构");
        parmOrganizationInfo.setAddress("测试地址");
        parmOrganizationInfo.setCity("北京");
        parmOrganizationInfo.setCountry("中国");
        parmOrganizationInfo.setStatus("0");
        parmOrganizationInfo.setProcessTodayFlag("1");
        parmOrganizationInfo.setAutoGenPymtDays(5L);
        parmOrganizationInfo.setMarkupFeeRate(new BigDecimal("0.05"));
        parmOrganizationInfo.setVersionNumber(1L);
        parmOrganizationInfo.setToday(LocalDate.now());
        parmOrganizationInfo.setNextProcessingDay(LocalDate.now().plusDays(1));
        parmOrganizationInfo.setLastProcessingDay(LocalDate.now().minusDays(1));
        parmOrganizationInfo.setCreateTime(LocalDateTime.now());
        parmOrganizationInfo.setUpdateTime(LocalDateTime.now());

        organizationInfoResDTO = new OrganizationInfoResDTO();
        organizationInfoResDTO.setId("1234567890123456");
        organizationInfoResDTO.setOrganizationNumber("001");
        organizationInfoResDTO.setDescription("测试机构");
        organizationInfoResDTO.setAddress("测试地址");
        organizationInfoResDTO.setCity("北京");
        organizationInfoResDTO.setCountry("中国");
        organizationInfoResDTO.setStatus("0");
        organizationInfoResDTO.setProcessTodayFlag("1");
        organizationInfoResDTO.setAutoGenPymtDays(5L);
        organizationInfoResDTO.setMarkupFeeRate(new BigDecimal("0.05"));
        organizationInfoResDTO.setVersionNumber(1L);
        organizationInfoResDTO.setToday(LocalDate.now());
        organizationInfoResDTO.setNextProcessingDay(LocalDate.now().plusDays(1));
        organizationInfoResDTO.setLastProcessingDay(LocalDate.now().minusDays(1));
        organizationInfoResDTO.setCreateTime(LocalDateTime.now());
        organizationInfoResDTO.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 测试新增机构参数 - 成功场景
     * 测试 OrganizationInfoServiceImpl.add() 方法
     */
    @Test
    void testAdd_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {

            // Arrange
            Mockito.lenient().when(parmOrganizationInfoSelfMapper.isExists(anyString())).thenReturn(0);
            Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(1234567890123456L);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copy(any(OrganizationInfoReqDTO.class), eq(ParmOrganizationInfo.class)))
                    .thenReturn(parmOrganizationInfo);

            // Act
            ParameterCompare result = organizationInfoService.add(organizationInfoReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("001", result.getMainParmId());
            verify(parmOrganizationInfoSelfMapper).isExists("001");
            verify(numberIdGenerator).generateId("001");
        }
    }

    /**
     * 测试新增机构参数 - 机构已存在异常
     * 测试 OrganizationInfoServiceImpl.add() 方法
     */
    @Test
    void testAdd_OrganizationExists() {
        // Arrange
        Mockito.lenient().when(parmOrganizationInfoSelfMapper.isExists(anyString())).thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> organizationInfoService.add(organizationInfoReqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_ORGANIZATION_INFO_FAULT, exception.getErrCode());
        verify(parmOrganizationInfoSelfMapper).isExists("001");
    }

    /**
     * 测试删除机构参数 - 成功场景
     * 测试 OrganizationInfoServiceImpl.remove() 方法
     */
    @Test
    void testRemove_Success() {
        // Arrange
        Mockito.lenient().when(parmOrganizationInfoMapper.selectByPrimaryKey(anyString())).thenReturn(parmOrganizationInfo);

        // Act
        ParameterCompare result = organizationInfoService.remove("1234567890123456");

        // Assert
        assertNotNull(result);
        assertEquals("001", result.getMainParmId());
        verify(parmOrganizationInfoMapper).selectByPrimaryKey("1234567890123456");
    }

    /**
     * 测试删除机构参数 - 数据不存在异常
     * 测试 OrganizationInfoServiceImpl.remove() 方法
     */
    @Test
    void testRemove_DataNotFound() {
        // Arrange
        Mockito.lenient().when(parmOrganizationInfoMapper.selectByPrimaryKey(anyString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> organizationInfoService.remove("nonexistent"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_ORGANIZATION_INFO_FAULT, exception.getErrCode());
        verify(parmOrganizationInfoMapper).selectByPrimaryKey("nonexistent");
    }

    /**
     * 测试查询机构参数 - 成功场景
     * 测试 OrganizationInfoServiceImpl.find() 方法
     */
    @Test
    void testFind_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange
            Mockito.lenient().when(parmOrganizationInfoMapper.selectByPrimaryKey(anyString())).thenReturn(parmOrganizationInfo);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmOrganizationInfo.class), eq(OrganizationInfoResDTO.class)))
                    .thenReturn(organizationInfoResDTO);

            // Act
            OrganizationInfoResDTO result = organizationInfoService.find("1234567890123456");

            // Assert
            assertNotNull(result);
            assertEquals("001", result.getOrganizationNumber());
            assertEquals("测试机构", result.getDescription());
            verify(parmOrganizationInfoMapper).selectByPrimaryKey("1234567890123456");
        }
    }

    /**
     * 测试查询机构参数 - 参数为空异常
     * 测试 OrganizationInfoServiceImpl.find() 方法
     */
    @Test
    void testFind_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> organizationInfoService.find(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, exception.getErrCode());
    }

    /**
     * 测试查询机构参数 - 数据不存在异常
     * 测试 OrganizationInfoServiceImpl.find() 方法
     */
    @Test
    void testFind_DataNotFound() {
        // Arrange
        Mockito.lenient().when(parmOrganizationInfoMapper.selectByPrimaryKey(anyString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> organizationInfoService.find("nonexistent"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_ORGANIZATION_INFO_FAULT, exception.getErrCode());
        verify(parmOrganizationInfoMapper).selectByPrimaryKey("nonexistent");
    }

    /**
     * 测试修改机构参数 - 成功场景
     * 测试 OrganizationInfoServiceImpl.modify() 方法
     */
    @Test
    void testModify_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange
            organizationInfoReqDTO.setId("1234567890123456");
            Mockito.lenient().when(parmOrganizationInfoMapper.selectByPrimaryKey(anyString())).thenReturn(parmOrganizationInfo);
            beanMappingMock.when(() -> BeanMapping.copy(any(OrganizationInfoReqDTO.class), eq(ParmOrganizationInfo.class)))
                    .thenReturn(parmOrganizationInfo);

            // Act
            ParameterCompare result = organizationInfoService.modify(organizationInfoReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("001", result.getMainParmId());
            verify(parmOrganizationInfoMapper).selectByPrimaryKey("1234567890123456");
        }
    }

    /**
     * 测试修改机构参数 - ID为空异常
     * 测试 OrganizationInfoServiceImpl.modify() 方法
     */
    @Test
    void testModify_NullId() {
        // Arrange
        organizationInfoReqDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> organizationInfoService.modify(organizationInfoReqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT, exception.getErrCode());
    }

    /**
     * 测试修改机构参数 - 机构号冲突异常
     * 测试 OrganizationInfoServiceImpl.modify() 方法
     */
    @Test
    void testModify_OrganizationNumberConflict() {
        // Arrange
        organizationInfoReqDTO.setId("1234567890123456");
        organizationInfoReqDTO.setOrganizationNumber("002"); // 修改为不同的机构号
        ParmOrganizationInfo existingOrg = new ParmOrganizationInfo();
        existingOrg.setOrganizationNumber("001"); // 原来的机构号
        Mockito.lenient().when(parmOrganizationInfoMapper.selectByPrimaryKey(anyString())).thenReturn(existingOrg);
        Mockito.lenient().when(parmOrganizationInfoSelfMapper.isExists("002")).thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> organizationInfoService.modify(organizationInfoReqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_ORGANIZATION_INFO_FAULT, exception.getErrCode());
        verify(parmOrganizationInfoMapper).selectByPrimaryKey("1234567890123456");
        verify(parmOrganizationInfoSelfMapper).isExists("002");
    }

    /**
     * 测试分页查询机构参数
     * 测试 OrganizationInfoServiceImpl.findListByPage() 方法
     */
    @Test
    void testFindListByPage_Success() {
        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {

            // Arrange
            Page<ParmOrganizationInfo> page = new Page<>(1, 10);
            page.setTotal(1);
            page.setPages(1);
            List<ParmOrganizationInfo> orgList = new ArrayList<>();
            orgList.add(parmOrganizationInfo);
            List<OrganizationInfoResDTO> resDTOList = new ArrayList<>();
            resDTOList.add(organizationInfoResDTO);

            pageHelperMock.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(page);
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("001");
            Mockito.lenient().when(parmOrganizationInfoSelfMapper.selectByCondition(anyString(), anyString())).thenReturn(orgList);
            beanMappingMock.when(() -> BeanMapping.copyList(any(List.class), eq(OrganizationInfoResDTO.class)))
                    .thenReturn(resDTOList);

            // Act
            PageResultDTO<OrganizationInfoResDTO> result = organizationInfoService.findListByPage(1, 10, "001", "测试");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getData().size());
            verify(parmOrganizationInfoSelfMapper).selectByCondition("001", "测试");
        }
    }

    /**
     * 测试根据机构号查询机构信息 - 成功场景
     * 测试 OrganizationInfoServiceImpl.findOrganizationInfo() 方法
     */
    @Test
    void testFindOrganizationInfo_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange
            Mockito.lenient().when(parmOrganizationInfoSelfMapper.selectByOrganizationNumber(anyString())).thenReturn(parmOrganizationInfo);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmOrganizationInfo.class), eq(OrganizationInfoResDTO.class)))
                    .thenReturn(organizationInfoResDTO);

            // Act
            OrganizationInfoResDTO result = organizationInfoService.findOrganizationInfo("001");

            // Assert
            assertNotNull(result);
            assertEquals("001", result.getOrganizationNumber());
            assertEquals("测试机构", result.getDescription());
            verify(parmOrganizationInfoSelfMapper).selectByOrganizationNumber("001");
        }
    }

    /**
     * 测试根据机构号查询机构信息 - 数据不存在异常
     * 测试 OrganizationInfoServiceImpl.findOrganizationInfo() 方法
     */
    @Test
    void testFindOrganizationInfo_DataNotFound() {
        // Arrange
        Mockito.lenient().when(parmOrganizationInfoSelfMapper.selectByOrganizationNumber(anyString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> organizationInfoService.findOrganizationInfo("nonexistent"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_ORGANIZATION_INFO_BY_ORG_FAULT, exception.getErrCode());
        verify(parmOrganizationInfoSelfMapper).selectByOrganizationNumber("nonexistent");
    }

    /**
     * 测试获取账单日 - 成功场景
     * 测试 OrganizationInfoServiceImpl.getOrgAutoCycleDay() 方法
     */
    @Test
    void testGetOrgAutoCycleDay_WithCycleDay() {
        // Arrange
        ParmOrganizationCycleDay cycleDay = new ParmOrganizationCycleDay();
        cycleDay.setCycleDay(15);
        List<ParmOrganizationCycleDay> cycleDayList = new ArrayList<>();
        cycleDayList.add(cycleDay);

        Mockito.lenient().when(parmOrganizationInfoSelfMapper.selectByOrganizationNumber(anyString())).thenReturn(parmOrganizationInfo);
        Mockito.lenient().when(cycleDaySelfMapper.selectByOrgNumber(anyString())).thenReturn(cycleDayList);

        // Act
        Integer result = organizationInfoService.getOrgAutoCycleDay("001");

        // Assert
        assertNotNull(result);
        assertEquals(15, result);
        verify(parmOrganizationInfoSelfMapper).selectByOrganizationNumber("001");
        verify(cycleDaySelfMapper).selectByOrgNumber("001");
    }

    /**
     * 测试获取账单日 - 无配置使用默认值
     * 测试 OrganizationInfoServiceImpl.getOrgAutoCycleDay() 方法
     */
    @Test
    void testGetOrgAutoCycleDay_DefaultValue() {
        // Arrange
        List<ParmOrganizationCycleDay> emptyCycleDayList = new ArrayList<>();
        LocalDate nextDay = LocalDate.of(2023, 5, 15);
        parmOrganizationInfo.setNextProcessingDay(nextDay);

        Mockito.lenient().when(parmOrganizationInfoSelfMapper.selectByOrganizationNumber(anyString())).thenReturn(parmOrganizationInfo);
        Mockito.lenient().when(cycleDaySelfMapper.selectByOrgNumber(anyString())).thenReturn(emptyCycleDayList);

        // Act
        Integer result = organizationInfoService.getOrgAutoCycleDay("001");

        // Assert
        assertNotNull(result);
        assertEquals(15, result); // 使用nextProcessingDay的日期部分
        verify(parmOrganizationInfoSelfMapper).selectByOrganizationNumber("001");
        verify(cycleDaySelfMapper).selectByOrgNumber("001");
    }

    /**
     * 测试机构日切方法
     * 测试 OrganizationInfoServiceImpl.cutOver() 方法
     */
    @Test
    void testCutOver_Success() {
        // Act & Assert
        assertDoesNotThrow(() -> organizationInfoService.cutOver());
        // 由于方法内部逻辑被注释，这里主要验证方法不抛异常
    }

    /**
     * 测试查询所有机构信息
     * 测试 OrganizationInfoServiceImpl.findOrganizationInfoAll() 方法
     */
    @Test
    void testFindOrganizationInfoAll_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {

            // Arrange
            List<ParmOrganizationInfo> orgList = new ArrayList<>();
            orgList.add(parmOrganizationInfo);
            List<OrganizationInfoResDTO> resDTOList = new ArrayList<>();
            resDTOList.add(organizationInfoResDTO);

            orgUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            Mockito.lenient().when(parmOrganizationInfoSelfMapper.selectAll(anyString(), anyBoolean())).thenReturn(orgList);
            beanMappingMock.when(() -> BeanMapping.copyList(any(List.class), eq(OrganizationInfoResDTO.class)))
                    .thenReturn(resDTOList);

            // Act
            List<OrganizationInfoResDTO> result = organizationInfoService.findOrganizationInfoAll(false);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("001", result.get(0).getOrganizationNumber());
            verify(parmOrganizationInfoSelfMapper).selectAll("001", true);
        }
    }

    /**
     * 测试数据库更新方法
     * 测试 OrganizationInfoServiceImpl.updateDb() 方法
     */
    @Test
    void testUpdateDb_Success() {
        try (MockedStatic<JSON> jsonMock = mockStatic(JSON.class)) {
            // Arrange
            ParmModificationRecord record = new ParmModificationRecord();
            record.setParmBody("{\"id\":\"123\",\"organizationNumber\":\"001\"}");
            
            jsonMock.when(() -> JSON.parseObject(anyString(), eq(ParmOrganizationInfo.class)))
                    .thenReturn(parmOrganizationInfo);
            Mockito.lenient().when(parmOrganizationInfoMapper.updateByPrimaryKeySelective(any(ParmOrganizationInfo.class))).thenReturn(1);

            // Act
            boolean result = organizationInfoService.updateDb(record);

            // Assert
            assertTrue(result);
            verify(parmOrganizationInfoMapper).updateByPrimaryKeySelective(any(ParmOrganizationInfo.class));
        }
    }

    /**
     * 测试数据库插入方法
     * 测试 OrganizationInfoServiceImpl.insertDb() 方法
     */
    @Test
    void testInsertDb_Success() {
        try (MockedStatic<JSON> jsonMock = mockStatic(JSON.class)) {
            // Arrange
            ParmModificationRecord record = new ParmModificationRecord();
            record.setParmBody("{\"id\":\"123\",\"organizationNumber\":\"001\"}");
            
            jsonMock.when(() -> JSON.parseObject(anyString(), eq(ParmOrganizationInfo.class)))
                    .thenReturn(parmOrganizationInfo);
            Mockito.lenient().when(parmOrganizationInfoMapper.insertSelective(any(ParmOrganizationInfo.class))).thenReturn(1);

            // Act
            boolean result = organizationInfoService.insertDb(record);

            // Assert
            assertTrue(result);
            verify(parmOrganizationInfoMapper).insertSelective(any(ParmOrganizationInfo.class));
        }
    }

    /**
     * 测试数据库删除方法
     * 测试 OrganizationInfoServiceImpl.deleteDb() 方法
     */
    @Test
    void testDeleteDb_Success() {
        try (MockedStatic<JSON> jsonMock = mockStatic(JSON.class)) {
            // Arrange
            ParmModificationRecord record = new ParmModificationRecord();
            record.setParmBody("{\"id\":\"123\",\"organizationNumber\":\"001\"}");
            
            jsonMock.when(() -> JSON.parseObject(anyString(), eq(ParmOrganizationInfo.class)))
                    .thenReturn(parmOrganizationInfo);
            Mockito.lenient().when(parmOrganizationInfoMapper.deleteByPrimaryKey(anyString())).thenReturn(1);

            // Act
            boolean result = organizationInfoService.deleteDb(record);

            // Assert
            assertTrue(result);
            verify(parmOrganizationInfoMapper).deleteByPrimaryKey(anyString());
        }
    }
}