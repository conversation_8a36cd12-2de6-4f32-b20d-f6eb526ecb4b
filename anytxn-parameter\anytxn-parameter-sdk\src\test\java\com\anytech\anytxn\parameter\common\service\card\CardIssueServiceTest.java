package com.anytech.anytxn.parameter.common.service.card;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.card.service.CardIssueServiceImpl;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardIssueMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardIssueSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardIssueReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardIssueResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardIssue;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CardIssueServiceTest test class
 * 
 * <AUTHOR> Engineer
 * @date 2025-01-23
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CardIssueServiceTest {

    @Mock
    private ParmCardIssueSelfMapper parmCardIssueSelfMapper;

    @Mock
    private ParmCardIssueMapper parmCardIssueMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private CardIssueServiceImpl cardIssueService;

    private ParmCardIssue parmCardIssue;
    private CardIssueReqDTO cardIssueReqDTO;
    private CardIssueResDTO cardIssueResDTO;

    @BeforeEach
    void setUp() {
        // Mock OrgNumberUtils static method to avoid NPE in BaseParam constructor
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");

            // Create test entity object
            parmCardIssue = new ParmCardIssue();
        parmCardIssue.setId("1");
        parmCardIssue.setOrganizationNumber("1001");
        parmCardIssue.setTableId("ISSUE001");
        parmCardIssue.setDescription("Test Card Issue");
        parmCardIssue.setFirstIssueMonths(24);
        parmCardIssue.setActiveFirstIssueFlag("1");
        parmCardIssue.setCardReissueMonths(36);
        parmCardIssue.setReissueAdvanceMonths(3);
        parmCardIssue.setStatus("1");
        parmCardIssue.setEmergencyMakeCardMonth(12);
        parmCardIssue.setAutoCloseCardDays(30);
        parmCardIssue.setCreateTime(LocalDateTime.now());
        parmCardIssue.setUpdateTime(LocalDateTime.now());
        parmCardIssue.setUpdateBy("testUser");
        parmCardIssue.setVersionNumber(1L);

        // Create test request DTO object
        cardIssueReqDTO = new CardIssueReqDTO();
        cardIssueReqDTO.setId("1");
        cardIssueReqDTO.setOrganizationNumber("1001");
        cardIssueReqDTO.setTableId("ISSUE001");
        cardIssueReqDTO.setDescription("Test Card Issue");
        cardIssueReqDTO.setFirstIssueMonths(24);
        cardIssueReqDTO.setActiveFirstIssueFlag("1");
        cardIssueReqDTO.setCardReissueMonths(36);
        cardIssueReqDTO.setReissueAdvanceMonths(3);
        cardIssueReqDTO.setStatus("1");
        cardIssueReqDTO.setEmergencyMakeCardMonth(12);
        cardIssueReqDTO.setAutoCloseCardDays(30);
        cardIssueReqDTO.setUpdateBy("testUser");
        cardIssueReqDTO.setVersionNumber(1L);

        // Create test response DTO object
        cardIssueResDTO = new CardIssueResDTO();
        cardIssueResDTO.setId("1");
        cardIssueResDTO.setOrganizationNumber("1001");
        cardIssueResDTO.setTableId("ISSUE001");
        cardIssueResDTO.setDescription("Test Card Issue");
        cardIssueResDTO.setFirstIssueMonths(24);
        cardIssueResDTO.setActiveFirstIssueFlag("1");
        cardIssueResDTO.setCardReissueMonths(36);
        cardIssueResDTO.setReissueAdvanceMonths(3);
        cardIssueResDTO.setStatus("1");
        cardIssueResDTO.setEmergencyMakeCardMonth(12);
        cardIssueResDTO.setAutoCloseCardDays(30);
        cardIssueResDTO.setCreateTime(LocalDateTime.now());
        cardIssueResDTO.setUpdateTime(LocalDateTime.now());
        cardIssueResDTO.setUpdateBy("testUser");
        cardIssueResDTO.setVersionNumber(1L);
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        Mockito.lenient().when(parmCardIssueSelfMapper.selectByOrgAndTableId("1001", "ISSUE001")).thenReturn(parmCardIssue);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardIssue, CardIssueResDTO.class))
                    .thenReturn(cardIssueResDTO);

            // Act
            CardIssueResDTO result = cardIssueService.findByOrgAndTableId("1001", "ISSUE001");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("ISSUE001", result.getTableId());
            assertEquals("Test Card Issue", result.getDescription());
            verify(parmCardIssueSelfMapper).selectByOrgAndTableId("1001", "ISSUE001");
        }
    }

    @Test
    void testFindByOrgAndTableId_NotFound() {
        // Arrange
        Mockito.lenient().when(parmCardIssueSelfMapper.selectByOrgAndTableId("1001", "ISSUE999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.findByOrgAndTableId("1001", "ISSUE999"));

        assertEquals(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_FAULT.getCode(), exception.getErrCode());
        verify(parmCardIssueSelfMapper).selectByOrgAndTableId("1001", "ISSUE999");
    }

    @Test
    void testAdd_Success() {
        // Arrange
        Mockito.lenient().when(parmCardIssueSelfMapper.isExists("1001", "ISSUE001")).thenReturn(0);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);

        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {

            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockTenantUtils.when(() -> TenantUtils.getTenantId()).thenReturn("tenant1");
            mockBeanMapping.when(() -> BeanMapping.copy(cardIssueReqDTO, ParmCardIssue.class))
                    .thenReturn(parmCardIssue);

            // Act
            ParameterCompare result = cardIssueService.add(cardIssueReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("ISSUE001", result.getMainParmId());
            verify(parmCardIssueSelfMapper).isExists("1001", "ISSUE001");
            verify(numberIdGenerator).generateId("tenant1");
        }
    }

    @Test
    void testAdd_NullRequest() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.add(null));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testAdd_NullOrganizationNumber() {
        // Arrange
        cardIssueReqDTO.setOrganizationNumber(null);
        Mockito.lenient().when(parmCardIssueSelfMapper.isExists("1001", "ISSUE001")).thenReturn(0);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);

        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {

            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockTenantUtils.when(() -> TenantUtils.getTenantId()).thenReturn("tenant1");
            mockBeanMapping.when(() -> BeanMapping.copy(cardIssueReqDTO, ParmCardIssue.class))
                    .thenReturn(parmCardIssue);

            // Act
            ParameterCompare result = cardIssueService.add(cardIssueReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("1001", cardIssueReqDTO.getOrganizationNumber());
            verify(parmCardIssueSelfMapper).isExists("1001", "ISSUE001");
        }
    }

    @Test
    void testAdd_AlreadyExists() {
        // Arrange
        Mockito.lenient().when(parmCardIssueSelfMapper.isExists("1001", "ISSUE001")).thenReturn(1);

        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> cardIssueService.add(cardIssueReqDTO));

            assertEquals(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_CARD_ISSUE_FAULT.getCode(), exception.getErrCode());
            verify(parmCardIssueSelfMapper).isExists("1001", "ISSUE001");
        }
    }

    @Test
    void testRemoveById_Success() {
        // Arrange
        Mockito.lenient().when(parmCardIssueMapper.selectByPrimaryKey("1")).thenReturn(parmCardIssue);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            // Act
            ParameterCompare result = cardIssueService.removeById("1");

            // Assert
            assertNotNull(result);
            assertEquals("ISSUE001", result.getMainParmId());
            verify(parmCardIssueMapper).selectByPrimaryKey("1");
        }
    }

    @Test
    void testRemoveById_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.removeById(null));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testRemoveById_NotFound() {
        // Arrange
        Mockito.lenient().when(parmCardIssueMapper.selectByPrimaryKey("999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.removeById("999"));

        assertEquals(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmCardIssueMapper).selectByPrimaryKey("999");
    }

    @Test
    void testRemoveByOrgAndTableId_Success() {
        // Arrange
        Mockito.lenient().when(parmCardIssueSelfMapper.selectByOrgAndTableId("1001", "ISSUE001")).thenReturn(parmCardIssue);

        // Act
        ParameterCompare result = cardIssueService.removeByOrgAndTableId("1001", "ISSUE001");

        // Assert
        assertNotNull(result);
        assertEquals("ISSUE001", result.getMainParmId());
        verify(parmCardIssueSelfMapper).selectByOrgAndTableId("1001", "ISSUE001");
    }

    @Test
    void testRemoveByOrgAndTableId_EmptyOrganizationNumber() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.removeByOrgAndTableId("", "ISSUE001"));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testRemoveByOrgAndTableId_EmptyTableId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.removeByOrgAndTableId("1001", ""));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testRemoveByOrgAndTableId_NotFound() {
        // Arrange
        Mockito.lenient().when(parmCardIssueSelfMapper.selectByOrgAndTableId("1001", "ISSUE999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.removeByOrgAndTableId("1001", "ISSUE999"));

        assertEquals(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmCardIssueSelfMapper).selectByOrgAndTableId("1001", "ISSUE999");
    }

    @Test
    void testModify_Success() {
        // Arrange
        ParmCardIssue oldParmCardIssue = new ParmCardIssue();
        oldParmCardIssue.setId("1");
        oldParmCardIssue.setOrganizationNumber("1001");
        oldParmCardIssue.setTableId("ISSUE001");
        oldParmCardIssue.setDescription("Old Description");

        Mockito.lenient().when(parmCardIssueMapper.selectByPrimaryKey("1")).thenReturn(oldParmCardIssue);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(cardIssueReqDTO, ParmCardIssue.class))
                    .thenReturn(parmCardIssue);

            // Act
            ParameterCompare result = cardIssueService.modify(cardIssueReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("ISSUE001", result.getMainParmId());
            verify(parmCardIssueMapper).selectByPrimaryKey("1");
        }
    }

    @Test
    void testModify_NullRequest() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.modify(null));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testModify_DifferentOrgAndTableId_AlreadyExists() {
        // Arrange
        ParmCardIssue oldParmCardIssue = new ParmCardIssue();
        oldParmCardIssue.setId("1");
        oldParmCardIssue.setOrganizationNumber("1002");
        oldParmCardIssue.setTableId("ISSUE002");

        cardIssueReqDTO.setOrganizationNumber("1001");
        cardIssueReqDTO.setTableId("ISSUE001");

        Mockito.lenient().when(parmCardIssueMapper.selectByPrimaryKey("1")).thenReturn(oldParmCardIssue);
        Mockito.lenient().when(parmCardIssueSelfMapper.isExists("1001", "ISSUE001")).thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.modify(cardIssueReqDTO));

        assertEquals(AnyTxnParameterRespCodeEnum.S_EXIST_PARM_CARD_ISSUE_FAULT.getCode(), exception.getErrCode());
        verify(parmCardIssueMapper).selectByPrimaryKey("1");
        verify(parmCardIssueSelfMapper).isExists("1001", "ISSUE001");
    }

    @Test
    void testFindById_Success() {
        // Arrange
        Mockito.lenient().when(parmCardIssueMapper.selectByPrimaryKey("1")).thenReturn(parmCardIssue);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardIssue, CardIssueResDTO.class))
                    .thenReturn(cardIssueResDTO);

            // Act
            CardIssueResDTO result = cardIssueService.findById("1");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("ISSUE001", result.getTableId());
            verify(parmCardIssueMapper).selectByPrimaryKey("1");
        }
    }

    @Test
    void testFindById_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.findById(null));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testFindById_NotFound() {
        // Arrange
        Mockito.lenient().when(parmCardIssueMapper.selectByPrimaryKey("999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> cardIssueService.findById("999"));

        assertEquals(AnyTxnParameterRespCodeEnum.S_GET_PARM_CARD_ISSUE_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmCardIssueMapper).selectByPrimaryKey("999");
    }

    @Test
    void testFindList_Success() {
        // Arrange
        List<ParmCardIssue> parmCardIssueList = Arrays.asList(parmCardIssue);
        List<CardIssueResDTO> cardIssueResDTOList = Arrays.asList(cardIssueResDTO);
        Page<ParmCardIssue> page = new Page<>(1, 10);
        page.setTotal(1);
        page.setPages(1);

        Mockito.lenient().when(parmCardIssueSelfMapper.selectByCondition("ISSUE001", "Test", "1001"))
                .thenReturn(parmCardIssueList);

        try (MockedStatic<PageHelper> mockPageHelper = mockStatic(PageHelper.class)) {
            mockPageHelper.when(() -> PageHelper.startPage(1, 10)).thenReturn(page);
            
            try (MockedStatic<StringUtils> mockStringUtils = mockStatic(StringUtils.class)) {
                mockStringUtils.when(() -> StringUtils.isEmpty("1001")).thenReturn(false);
                
                try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
                    mockBeanMapping.when(() -> BeanMapping.copyList(parmCardIssueList, CardIssueResDTO.class))
                            .thenReturn(cardIssueResDTOList);

                    // Act
                    PageResultDTO<CardIssueResDTO> result = cardIssueService.findList(1, 10, "ISSUE001", "Test", "1001");

                    // Assert
                    assertNotNull(result);
                    assertEquals(1, result.getPage());
                    assertEquals(10, result.getRows());
                    assertEquals(1, result.getTotal());
                    assertEquals(1, result.getTotalPage());
                    assertEquals(1, result.getData().size());
                    verify(parmCardIssueSelfMapper).selectByCondition("ISSUE001", "Test", "1001");
                }
            }
        }
    }

    @Test
    void testFindList_EmptyOrganizationNumber() {
        // Arrange
        List<ParmCardIssue> parmCardIssueList = Arrays.asList(parmCardIssue);
        List<CardIssueResDTO> cardIssueResDTOList = Arrays.asList(cardIssueResDTO);
        Page<ParmCardIssue> page = new Page<>(1, 10);
        page.setTotal(1);
        page.setPages(1);

        try (MockedStatic<PageHelper> mockPageHelper = mockStatic(PageHelper.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<StringUtils> mockStringUtils = mockStatic(StringUtils.class)) {

            mockPageHelper.when(() -> PageHelper.startPage(1, 10)).thenReturn(page);
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockStringUtils.when(() -> StringUtils.isEmpty("")).thenReturn(true);
            mockBeanMapping.when(() -> BeanMapping.copyList(parmCardIssueList, CardIssueResDTO.class))
                    .thenReturn(cardIssueResDTOList);

            Mockito.lenient().when(parmCardIssueSelfMapper.selectByCondition("ISSUE001", "Test", "1001"))
                    .thenReturn(parmCardIssueList);

            // Act
            PageResultDTO<CardIssueResDTO> result = cardIssueService.findList(1, 10, "ISSUE001", "Test", "");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getData().size());
            verify(parmCardIssueSelfMapper).selectByCondition("ISSUE001", "Test", "1001");
        }
    }


}